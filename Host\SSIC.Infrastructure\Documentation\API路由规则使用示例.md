# API路由规则使用示例

## 路由规则概述

**标准格式**: `api/{模块名称}/{控制器名称}/{方法名称}`

所有模块的API都将遵循这个统一的路由格式，确保API的一致性和可预测性。

## 控制器示例

### 1. 基础控制器示例

```csharp
namespace SSIC.Modules.Auth.Controllers
{
    /// <summary>
    /// 用户管理控制器
    /// 模块: Auth
    /// 控制器: User
    /// 基础路由: api/auth/user
    /// </summary>
    public class UserController : ControllerBase
    {
        private readonly ILogger<UserController> _logger;

        public UserController(ILogger<UserController> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// 获取所有用户
        /// 路由: GET api/auth/user/getall
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> GetAll()
        {
            // 实现逻辑
            return Ok(new { message = "获取所有用户" });
        }

        /// <summary>
        /// 根据ID获取用户
        /// 路由: GET api/auth/user/getbyid/{id}
        /// </summary>
        [HttpGet("{id:int}")]
        public async Task<IActionResult> GetById(int id)
        {
            // 实现逻辑
            return Ok(new { message = $"获取用户ID: {id}" });
        }

        /// <summary>
        /// 创建用户
        /// 路由: POST api/auth/user/create
        /// </summary>
        [HttpPost]
        public async Task<IActionResult> Create([FromBody] CreateUserRequest request)
        {
            // 实现逻辑
            return Ok(new { message = "创建用户成功" });
        }

        /// <summary>
        /// 更新用户
        /// 路由: PUT api/auth/user/update/{id}
        /// </summary>
        [HttpPut("{id:int}")]
        public async Task<IActionResult> Update(int id, [FromBody] UpdateUserRequest request)
        {
            // 实现逻辑
            return Ok(new { message = $"更新用户ID: {id}" });
        }

        /// <summary>
        /// 删除用户
        /// 路由: DELETE api/auth/user/delete/{id}
        /// </summary>
        [HttpDelete("{id:int}")]
        public async Task<IActionResult> Delete(int id)
        {
            // 实现逻辑
            return Ok(new { message = $"删除用户ID: {id}" });
        }
    }
}
```

### 2. 自定义路由示例

```csharp
namespace SSIC.Modules.Sys.Controllers
{
    /// <summary>
    /// 系统配置控制器
    /// 模块: Sys
    /// 控制器: SystemConfig
    /// 基础路由: api/sys/systemconfig
    /// </summary>
    public class SystemConfigController : ControllerBase
    {
        /// <summary>
        /// 获取配置列表
        /// 路由: GET api/sys/systemconfig/list
        /// </summary>
        [HttpGet("list")]
        public async Task<IActionResult> GetConfigList()
        {
            return Ok(new { message = "获取配置列表" });
        }

        /// <summary>
        /// 根据分类获取配置
        /// 路由: GET api/sys/systemconfig/category/{category}
        /// </summary>
        [HttpGet("category/{category}")]
        public async Task<IActionResult> GetByCategory(string category)
        {
            return Ok(new { message = $"获取分类配置: {category}" });
        }

        /// <summary>
        /// 搜索配置
        /// 路由: GET api/sys/systemconfig/search
        /// 查询参数: ?keyword=xxx&category=yyy
        /// </summary>
        [HttpGet("search")]
        public async Task<IActionResult> SearchConfigs([FromQuery] string keyword, [FromQuery] string? category = null)
        {
            return Ok(new { message = $"搜索配置: {keyword}, 分类: {category}" });
        }

        /// <summary>
        /// 批量更新配置
        /// 路由: POST api/sys/systemconfig/batchupdate
        /// </summary>
        [HttpPost("batchupdate")]
        public async Task<IActionResult> BatchUpdate([FromBody] BatchUpdateRequest request)
        {
            return Ok(new { message = "批量更新配置成功" });
        }

        /// <summary>
        /// 导出配置
        /// 路由: GET api/sys/systemconfig/export
        /// </summary>
        [HttpGet("export")]
        public async Task<IActionResult> ExportConfigs([FromQuery] string format = "json")
        {
            return Ok(new { message = $"导出配置，格式: {format}" });
        }

        /// <summary>
        /// 导入配置
        /// 路由: POST api/sys/systemconfig/import
        /// </summary>
        [HttpPost("import")]
        public async Task<IActionResult> ImportConfigs([FromForm] IFormFile file)
        {
            return Ok(new { message = "导入配置成功" });
        }
    }
}
```

### 3. RESTful风格示例

```csharp
namespace SSIC.Modules.Sys.Controllers
{
    /// <summary>
    /// 产品管理控制器 (RESTful风格)
    /// 模块: Sys
    /// 控制器: Product
    /// 基础路由: api/sys/product
    /// </summary>
    public class ProductController : ControllerBase
    {
        /// <summary>
        /// 获取产品列表
        /// 路由: GET api/sys/product/index
        /// </summary>
        [HttpGet("index")]
        public async Task<IActionResult> Index([FromQuery] int page = 1, [FromQuery] int size = 10)
        {
            return Ok(new { message = $"获取产品列表，页码: {page}, 大小: {size}" });
        }

        /// <summary>
        /// 获取产品详情
        /// 路由: GET api/sys/product/show/{id}
        /// </summary>
        [HttpGet("show/{id:int}")]
        public async Task<IActionResult> Show(int id)
        {
            return Ok(new { message = $"获取产品详情: {id}" });
        }

        /// <summary>
        /// 创建产品
        /// 路由: POST api/sys/product/store
        /// </summary>
        [HttpPost("store")]
        public async Task<IActionResult> Store([FromBody] CreateProductRequest request)
        {
            return Ok(new { message = "创建产品成功" });
        }

        /// <summary>
        /// 更新产品
        /// 路由: PUT api/sys/product/update/{id}
        /// </summary>
        [HttpPut("update/{id:int}")]
        public async Task<IActionResult> Update(int id, [FromBody] UpdateProductRequest request)
        {
            return Ok(new { message = $"更新产品: {id}" });
        }

        /// <summary>
        /// 删除产品
        /// 路由: DELETE api/sys/product/destroy/{id}
        /// </summary>
        [HttpDelete("destroy/{id:int}")]
        public async Task<IActionResult> Destroy(int id)
        {
            return Ok(new { message = $"删除产品: {id}" });
        }
    }
}
```

## HTTP测试文件示例

### Auth模块测试 (SSIC.Modules.Auth.http)

```http
@SSIC.Modules.Auth_HostAddress = http://localhost:5129
@token = Bearer your_jwt_token_here

### ========== 用户管理接口测试 ==========

### 获取所有用户
GET {{SSIC.Modules.Auth_HostAddress}}/api/auth/user/getall
Accept: application/json
Authorization: {{token}}

### 根据ID获取用户
GET {{SSIC.Modules.Auth_HostAddress}}/api/auth/user/getbyid/1
Accept: application/json
Authorization: {{token}}

### 创建用户
POST {{SSIC.Modules.Auth_HostAddress}}/api/auth/user/create
Content-Type: application/json
Authorization: {{token}}

{
  "username": "testuser",
  "email": "<EMAIL>",
  "password": "password123"
}

### 更新用户
PUT {{SSIC.Modules.Auth_HostAddress}}/api/auth/user/update/1
Content-Type: application/json
Authorization: {{token}}

{
  "username": "updateduser",
  "email": "<EMAIL>"
}

### 删除用户
DELETE {{SSIC.Modules.Auth_HostAddress}}/api/auth/user/delete/1
Authorization: {{token}}
```

### Sys模块测试 (SSIC.Modules.Sys.http)

```http
@SSIC.Modules.Sys_HostAddress = http://localhost:5129

### ========== 系统配置接口测试 ==========

### 获取配置列表
GET {{SSIC.Modules.Sys_HostAddress}}/api/sys/systemconfig/list
Accept: application/json

### 根据分类获取配置
GET {{SSIC.Modules.Sys_HostAddress}}/api/sys/systemconfig/category/database
Accept: application/json

### 搜索配置
GET {{SSIC.Modules.Sys_HostAddress}}/api/sys/systemconfig/search?keyword=connection&category=database
Accept: application/json

### 批量更新配置
POST {{SSIC.Modules.Sys_HostAddress}}/api/sys/systemconfig/batchupdate
Content-Type: application/json

{
  "configs": [
    {
      "key": "database.connection",
      "value": "new_connection_string"
    },
    {
      "key": "cache.timeout",
      "value": "3600"
    }
  ]
}

### 导出配置
GET {{SSIC.Modules.Sys_HostAddress}}/api/sys/systemconfig/export?format=json
Accept: application/json

### ========== 产品管理接口测试 ==========

### 获取产品列表
GET {{SSIC.Modules.Sys_HostAddress}}/api/sys/product/index?page=1&size=10
Accept: application/json

### 获取产品详情
GET {{SSIC.Modules.Sys_HostAddress}}/api/sys/product/show/1
Accept: application/json

### 创建产品
POST {{SSIC.Modules.Sys_HostAddress}}/api/sys/product/store
Content-Type: application/json

{
  "name": "新产品",
  "description": "产品描述",
  "price": 99.99,
  "category": "电子产品"
}

### 更新产品
PUT {{SSIC.Modules.Sys_HostAddress}}/api/sys/product/update/1
Content-Type: application/json

{
  "name": "更新的产品",
  "description": "更新的描述",
  "price": 199.99
}

### 删除产品
DELETE {{SSIC.Modules.Sys_HostAddress}}/api/sys/product/destroy/1
```

## 路由规则总结

### 1. 标准路由格式
- **格式**: `api/{模块名}/{控制器名}/{方法名}`
- **示例**: `api/auth/user/getall`

### 2. 参数路由
- **格式**: `api/{模块名}/{控制器名}/{方法名}/{参数}`
- **示例**: `api/auth/user/getbyid/1`

### 3. 自定义路由
- **格式**: `api/{模块名}/{控制器名}/{自定义路径}`
- **示例**: `api/sys/systemconfig/category/database`

### 4. 查询参数
- **格式**: `api/{模块名}/{控制器名}/{方法名}?param1=value1&param2=value2`
- **示例**: `api/sys/systemconfig/search?keyword=connection&category=database`

### 5. 完全自定义路由
- **使用Route属性**: `[Route("api/custom/path")]`
- **优先级最高**: 覆盖默认路由规则

## 最佳实践

1. **方法命名**: 使用清晰的动词命名，如 `GetAll`, `GetById`, `Create`, `Update`, `Delete`
2. **参数约束**: 使用路由约束，如 `{id:int}`, `{category:alpha}`
3. **HTTP方法**: 正确使用HTTP动词 (GET, POST, PUT, DELETE)
4. **文档注释**: 为所有API方法添加XML文档注释
5. **一致性**: 在同一模块内保持路由风格的一致性

通过遵循这些规则和示例，可以确保所有模块的API都具有一致的路由结构，便于开发、测试和维护。
