using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.AspNetCore.Mvc.Infrastructure;
using Microsoft.AspNetCore.OpenApi;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi.Models;
using SSIC.Infrastructure.Startups.Endpoints;
using System.Reflection;

namespace SSIC.Infrastructure.OpenApi
{
    /// <summary>
    /// 动态模块OpenAPI文档转换器
    /// 用于将动态加载的模块控制器添加到OpenAPI文档中
    /// </summary>
    public class DynamicModuleDocumentTransformer : IOpenApiDocumentTransformer
    {
        private readonly ILogger<DynamicModuleDocumentTransformer> _logger;
        private readonly IServiceProvider _serviceProvider;
        private static readonly HashSet<string> _processedPaths = new HashSet<string>();

        public DynamicModuleDocumentTransformer(
            ILogger<DynamicModuleDocumentTransformer> logger,
            IServiceProvider serviceProvider)
        {
            _logger = logger;
            _serviceProvider = serviceProvider;
        }

        public async Task TransformAsync(OpenApiDocument document, OpenApiDocumentTransformerContext context, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("开始转换OpenAPI文档以包含动态模块...");

                // 清除之前处理的路径记录
                _processedPaths.Clear();

                // 获取动态端点管理器来获取当前活跃的模块
                var endpointManager = DynamicEndpointManager.Instance;
                var activeModules = GetActiveModuleAssemblies(endpointManager);

                _logger.LogInformation("发现 {Count} 个活跃模块程序集", activeModules.Count);

                // 清除现有的动态模块路径（保留系统路径）
                ClearDynamicModulePaths(document);

                foreach (var assembly in activeModules)
                {
                    await ProcessModuleAssembly(document, assembly, cancellationToken);
                }

                _logger.LogInformation("OpenAPI文档转换完成，当前包含 {PathCount} 个路径", document.Paths.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "转换OpenAPI文档时出错");
            }
        }

        private List<Assembly> GetActiveModuleAssemblies(DynamicEndpointManager endpointManager)
        {
            var activeModules = new List<Assembly>();

            // 从AppDomain获取所有模块程序集
            var allModuleAssemblies = AppDomain.CurrentDomain.GetAssemblies()
                .Where(a => a.GetName().Name?.StartsWith("SSIC.Modules.") == true)
                .ToList();

            // 过滤出仍然活跃的模块（未被卸载的）
            foreach (var assembly in allModuleAssemblies)
            {
                var assemblyName = assembly.GetName().Name;
                if (assemblyName != null && !endpointManager.IsModuleUnloaded(assemblyName))
                {
                    activeModules.Add(assembly);
                    _logger.LogDebug("活跃模块: {AssemblyName}", assemblyName);
                }
                else
                {
                    _logger.LogDebug("跳过已卸载模块: {AssemblyName}", assemblyName);
                }
            }

            return activeModules;
        }

        private void ClearDynamicModulePaths(OpenApiDocument document)
        {
            // 保留系统路径，移除模块路径
            var systemPaths = new[] { "/api/endpoints/", "/api/openapi/", "/WeatherForecast" };
            var pathsToRemove = new List<string>();

            foreach (var path in document.Paths.Keys)
            {
                bool isSystemPath = systemPaths.Any(sp => path.StartsWith(sp));
                if (!isSystemPath)
                {
                    pathsToRemove.Add(path);
                }
            }

            foreach (var path in pathsToRemove)
            {
                document.Paths.Remove(path);
                _logger.LogDebug("移除路径: {Path}", path);
            }
        }

        private async Task ProcessModuleAssembly(OpenApiDocument document, Assembly assembly, CancellationToken cancellationToken)
        {
            try
            {
                var assemblyName = assembly.GetName().Name;
                var moduleName = assemblyName?.Replace("SSIC.Modules.", "") ?? "Unknown";
                
                _logger.LogDebug("处理模块程序集: {AssemblyName}", assemblyName);

                // 获取程序集中的所有控制器类型
                var controllerTypes = assembly.GetExportedTypes()
                    .Where(t => !t.IsAbstract && 
                               (t.Name.EndsWith("Controller") || 
                                t.IsSubclassOf(typeof(ControllerBase))))
                    .ToList();

                foreach (var controllerType in controllerTypes)
                {
                    await ProcessController(document, controllerType, moduleName, cancellationToken);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理模块程序集 {AssemblyName} 时出错", assembly.GetName().Name);
            }
        }

        private async Task ProcessController(OpenApiDocument document, Type controllerType, string moduleName, CancellationToken cancellationToken)
        {
            try
            {
                var controllerName = controllerType.Name.Replace("Controller", "");
                _logger.LogDebug("处理控制器: {ControllerName}", controllerName);

                // 获取控制器的路由属性
                var routeAttribute = controllerType.GetCustomAttribute<RouteAttribute>();
                var controllerRoute = routeAttribute?.Template ?? "[controller]";
                
                // 替换占位符
                controllerRoute = controllerRoute.Replace("[controller]", controllerName);

                // 获取所有公共动作方法
                var actionMethods = controllerType.GetMethods(BindingFlags.Public | BindingFlags.Instance)
                    .Where(m => m.IsPublic && 
                               !m.IsSpecialName && 
                               m.DeclaringType == controllerType &&
                               m.GetCustomAttributes<NonActionAttribute>().Count() == 0)
                    .ToList();

                foreach (var method in actionMethods)
                {
                    await ProcessAction(document, method, controllerRoute, moduleName, cancellationToken);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理控制器 {ControllerType} 时出错", controllerType.Name);
            }
        }

        private async Task ProcessAction(OpenApiDocument document, MethodInfo method, string controllerRoute, string moduleName, CancellationToken cancellationToken)
        {
            try
            {
                var actionName = method.Name;
                
                // 获取HTTP方法属性
                var httpMethods = GetHttpMethods(method);
                if (!httpMethods.Any())
                {
                    // 如果没有明确的HTTP方法属性，根据方法名推断
                    httpMethods = InferHttpMethods(actionName);
                }

                // 获取路由模板
                var routeTemplate = GetRouteTemplate(method, controllerRoute, actionName, moduleName);
                
                _logger.LogDebug("处理动作: {ActionName}, 路由: {Route}, HTTP方法: {Methods}", 
                    actionName, routeTemplate, string.Join(",", httpMethods));

                // 为每个HTTP方法创建OpenAPI操作
                foreach (var httpMethod in httpMethods)
                {
                    await AddOperationToDocument(document, routeTemplate, httpMethod, method, moduleName, cancellationToken);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理动作方法 {MethodName} 时出错", method.Name);
            }
        }

        private List<string> GetHttpMethods(MethodInfo method)
        {
            var httpMethods = new List<string>();

            if (method.GetCustomAttribute<HttpGetAttribute>() != null)
                httpMethods.Add("GET");
            if (method.GetCustomAttribute<HttpPostAttribute>() != null)
                httpMethods.Add("POST");
            if (method.GetCustomAttribute<HttpPutAttribute>() != null)
                httpMethods.Add("PUT");
            if (method.GetCustomAttribute<HttpDeleteAttribute>() != null)
                httpMethods.Add("DELETE");
            if (method.GetCustomAttribute<HttpPatchAttribute>() != null)
                httpMethods.Add("PATCH");
            if (method.GetCustomAttribute<HttpOptionsAttribute>() != null)
                httpMethods.Add("OPTIONS");
            if (method.GetCustomAttribute<HttpHeadAttribute>() != null)
                httpMethods.Add("HEAD");

            return httpMethods;
        }

        private List<string> InferHttpMethods(string actionName)
        {
            var lowerActionName = actionName.ToLowerInvariant();
            
            if (lowerActionName.StartsWith("get") || lowerActionName.StartsWith("list") || lowerActionName.StartsWith("find"))
                return new List<string> { "GET" };
            if (lowerActionName.StartsWith("post") || lowerActionName.StartsWith("create") || lowerActionName.StartsWith("add"))
                return new List<string> { "POST" };
            if (lowerActionName.StartsWith("put") || lowerActionName.StartsWith("update") || lowerActionName.StartsWith("edit"))
                return new List<string> { "PUT" };
            if (lowerActionName.StartsWith("delete") || lowerActionName.StartsWith("remove"))
                return new List<string> { "DELETE" };
            if (lowerActionName.StartsWith("patch"))
                return new List<string> { "PATCH" };
                
            // 默认为GET
            return new List<string> { "GET" };
        }

        private string GetRouteTemplate(MethodInfo method, string controllerRoute, string actionName, string moduleName)
        {
            // 构建标准路由：api/{模块名}/{控制器名}/{方法名}
            var standardRoute = $"api/{moduleName.ToLowerInvariant()}/{controllerRoute.ToLowerInvariant()}/{actionName.ToLowerInvariant()}";

            // 检查HTTP方法属性中的路由模板
            var httpAttributes = method.GetCustomAttributes()
                .Where(attr => attr.GetType().Name.StartsWith("Http") && attr.GetType().Name.EndsWith("Attribute"))
                .ToList();

            foreach (var attr in httpAttributes)
            {
                var templateProperty = attr.GetType().GetProperty("Template");
                if (templateProperty != null)
                {
                    var template = templateProperty.GetValue(attr) as string;
                    if (!string.IsNullOrEmpty(template))
                    {
                        // 如果模板是参数（如 {id}），附加到标准路由后
                        if (template.StartsWith("{") && template.EndsWith("}"))
                        {
                            return $"{standardRoute}/{template}";
                        }
                        // 如果模板是完整的自定义路径且包含api前缀，直接使用
                        else if (template.StartsWith("api/"))
                        {
                            return template;
                        }
                        // 如果模板包含路径分隔符但不是完整路径，附加到标准路由后
                        else if (template.Contains("/"))
                        {
                            return $"{standardRoute}/{template}";
                        }
                        // 其他情况，替换方法名部分
                        else
                        {
                            return $"api/{moduleName.ToLowerInvariant()}/{controllerRoute.ToLowerInvariant()}/{template.ToLowerInvariant()}";
                        }
                    }
                }
            }

            // 检查方法级别的路由属性
            var routeAttribute = method.GetCustomAttribute<RouteAttribute>();
            if (routeAttribute != null && !string.IsNullOrEmpty(routeAttribute.Template))
            {
                // 如果是完整的路由模板，直接使用
                if (routeAttribute.Template.StartsWith("api/"))
                {
                    return routeAttribute.Template;
                }
                // 否则按标准格式构建
                return $"api/{moduleName.ToLowerInvariant()}/{controllerRoute.ToLowerInvariant()}/{routeAttribute.Template.ToLowerInvariant()}";
            }

            // 返回标准路由格式：api/{模块名}/{控制器名}/{方法名}
            return standardRoute;
        }

        private async Task AddOperationToDocument(OpenApiDocument document, string routeTemplate, string httpMethod, MethodInfo method, string moduleName, CancellationToken cancellationToken)
        {
            try
            {
                var fullPath = $"/{routeTemplate}";
                var pathKey = $"{fullPath}:{httpMethod}";

                // 检查是否已经处理过这个路径和方法的组合
                if (_processedPaths.Contains(pathKey))
                {
                    _logger.LogDebug("跳过重复路径: {HttpMethod} {Route}", httpMethod, routeTemplate);
                    return;
                }

                // 记录已处理的路径
                _processedPaths.Add(pathKey);

                // 确保路径存在
                if (!document.Paths.ContainsKey(fullPath))
                {
                    document.Paths[fullPath] = new OpenApiPathItem();
                }

                var pathItem = document.Paths[fullPath];
                var operation = CreateOperation(method, moduleName);

                // 根据HTTP方法添加操作
                switch (httpMethod.ToUpperInvariant())
                {
                    case "GET":
                        pathItem.Operations[OperationType.Get] = operation;
                        break;
                    case "POST":
                        pathItem.Operations[OperationType.Post] = operation;
                        break;
                    case "PUT":
                        pathItem.Operations[OperationType.Put] = operation;
                        break;
                    case "DELETE":
                        pathItem.Operations[OperationType.Delete] = operation;
                        break;
                    case "PATCH":
                        pathItem.Operations[OperationType.Patch] = operation;
                        break;
                    case "OPTIONS":
                        pathItem.Operations[OperationType.Options] = operation;
                        break;
                    case "HEAD":
                        pathItem.Operations[OperationType.Head] = operation;
                        break;
                }

                _logger.LogDebug("已添加操作: {HttpMethod} {Route}", httpMethod, routeTemplate);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "添加操作到文档时出错: {HttpMethod} {Route}", httpMethod, routeTemplate);
            }
        }

        private OpenApiOperation CreateOperation(MethodInfo method, string moduleName)
        {
            var operation = new OpenApiOperation
            {
                Tags = new List<OpenApiTag> { new OpenApiTag { Name = moduleName } },
                Summary = GetSummary(method),
                Description = GetDescription(method),
                OperationId = $"{method.DeclaringType?.Name}_{method.Name}",
                Responses = new OpenApiResponses
                {
                    ["200"] = new OpenApiResponse
                    {
                        Description = "Success"
                    }
                }
            };

            // 添加参数
            AddParameters(operation, method);

            return operation;
        }

        private string GetSummary(MethodInfo method)
        {
            // 这里可以从XML注释中提取摘要
            // 简化实现，直接返回方法名
            return $"{method.DeclaringType?.Name}.{method.Name}";
        }

        private string GetDescription(MethodInfo method)
        {
            // 这里可以从XML注释中提取描述
            // 简化实现，返回空字符串
            return string.Empty;
        }

        private void AddParameters(OpenApiOperation operation, MethodInfo method)
        {
            // 简化实现，这里可以根据方法参数添加OpenAPI参数
            // 实际实现中需要分析参数类型、属性等
            operation.Parameters = new List<OpenApiParameter>();
        }
    }
}
