# ServiceCollectionExtensions和Scalar动态文档架构说明

## 概述

本文档详细说明了SSIC模块化架构中ServiceCollectionExtensions和Scalar动态文档的实现机制，以及API路由规则的配置。

## 核心组件架构

### 1. ServiceCollectionExtensions 扩展方法

#### 1.1 依赖注入扩展 (`SSIC.Infrastructure.DependencyInjection.Extensions.ServiceCollectionExtensions`)

**功能**: 自动扫描和注册实现特定接口的服务类

**支持的生命周期接口**:
- `IScoped` - 作用域生命周期
- `ITransient` - 瞬态生命周期  
- `ISingleton` - 单例生命周期

**注册逻辑**:
```csharp
// 扫描所有程序集中的类型
var types = assemblies
    .SelectMany(a => a.GetTypes())
    .Where(t => t.IsClass && !t.IsAbstract && !t.IsGenericTypeDefinition)
    .ToList();

// 根据接口类型自动注册服务
if (typeof(IScoped).IsAssignableFrom(type))
{
    services.AddScoped(interfaceType, type);
}
```

#### 1.2 模块服务扩展 (`SSIC.Infrastructure.Startups.Extensions.ServiceCollectionExtensions`)

**功能**: 为动态加载的模块配置路由和服务

**核心方法**: `AddModuleServices(IServiceCollection services, Assembly moduleAssembly)`

**路由配置规则**: `api/{模块名称}/{控制器名称}/{方法名称}`

**实现机制**:
```csharp
// 1. 提取模块名称
var moduleName = assemblyName.Split('.')[2].ToLower(); // SSIC.Modules.Sys -> sys
var routePrefix = $"api/{moduleName}";

// 2. 应用路由约定
services.Configure<MvcOptions>(options =>
{
    options.Conventions.Add(new ModuleRoutePrefixConvention(moduleAssembly, routePrefix));
});
```

### 2. ModuleRoutePrefixConvention 路由约定

**功能**: 为模块控制器和动作方法应用统一的路由前缀

**路由生成规则**:
- 控制器路由: `api/{模块名}/{控制器名}`
- 动作方法路由: `api/{模块名}/{控制器名}/{方法名}`

**实现逻辑**:
```csharp
// 控制器级别路由
var controllerRoute = $"{_routePrefix}/{controllerName}";

// 动作方法级别路由
var fullActionRoute = $"{controllerRoute}/{actionName}";
```

### 3. Scalar动态文档系统

#### 3.1 OpenAPI配置 (`HostBuilder.ConfigureServices`)

**配置位置**: `SSIC.Infrastructure.Startups.HostBuilder`

**核心配置**:
```csharp
services.AddOpenApi(options =>
{
    // 基础文档信息
    options.AddDocumentTransformer((document, context, cancellationToken) =>
    {
        document.Info = new()
        {
            Title = "SSIC 模块化API",
            Version = "V1",
            Description = "SSIC 模块化架构API文档，支持动态模块加载"
        };
    });

    // 动态模块文档转换器
    options.AddDocumentTransformer<DynamicModuleDocumentTransformer>();

    // 操作转换器 - 为模块API添加标签
    options.AddOperationTransformer((operation, context, cancellationToken) =>
    {
        if (context.Description.ActionDescriptor is ControllerActionDescriptor controllerAction)
        {
            var assemblyName = controllerAction.ControllerTypeInfo.Assembly.GetName().Name;
            if (assemblyName?.StartsWith("SSIC.Modules.") == true)
            {
                var moduleName = assemblyName.Replace("SSIC.Modules.", "");
                operation.Tags = new List<OpenApiTag> { new OpenApiTag { Name = moduleName } };
            }
        }
    });

    // JWT认证配置（非开发环境）
    if (!env.IsDevelopment())
    {
        options.AddDocumentTransformer<BearerSecuritySchemeTransformer>();
    }
});
```

#### 3.2 DynamicModuleDocumentTransformer 动态模块文档转换器

**功能**: 将动态加载的模块控制器添加到OpenAPI文档中

**核心流程**:
1. **获取活跃模块**: 从AppDomain扫描所有`SSIC.Modules.*`程序集
2. **清理现有路径**: 移除之前的模块路径，保留系统路径
3. **处理模块程序集**: 扫描控制器类型并生成API文档
4. **生成路由模板**: 按照标准格式生成路径

**路由模板生成逻辑**:
```csharp
private string GetRouteTemplate(MethodInfo method, string controllerRoute, string actionName, string moduleName)
{
    // 标准路由格式: api/{模块名}/{控制器名}/{方法名}
    var standardRoute = $"api/{moduleName.ToLowerInvariant()}/{controllerRoute.ToLowerInvariant()}/{actionName.ToLowerInvariant()}";

    // 检查HTTP属性中的自定义模板
    // 支持参数路由: {id}
    // 支持自定义路径
    // 默认返回标准路由
    
    return standardRoute;
}
```

#### 3.3 BearerSecuritySchemeTransformer JWT认证转换器

**功能**: 为OpenAPI文档添加JWT Bearer认证支持

**应用场景**: 仅在非开发环境中启用

**实现机制**:
```csharp
// 添加JWT安全方案
document.Components.SecuritySchemes = new Dictionary<string, OpenApiSecurityScheme>
{
    [JwtBearerDefaults.AuthenticationScheme] = new OpenApiSecurityScheme
    {
        Type = SecuritySchemeType.Http,
        Scheme = JwtBearerDefaults.AuthenticationScheme.ToLower(),
        In = ParameterLocation.Header,
        BearerFormat = "Json Web Token"
    }
};

// 为所有操作添加安全要求
foreach (var operation in document.Paths.Values.SelectMany(path => path.Operations))
{
    operation.Value.Security.Add(new OpenApiSecurityRequirement { ... });
}
```

### 4. Scalar UI配置

**配置位置**: `HostBuilder.Configure`

**UI配置**:
```csharp
if (app.Environment.IsDevelopment())
{
    app.MapScalarApiReference(opt =>
    {
        opt.Title = "Scalar Example2";
        opt.Theme = ScalarTheme.Kepler;
        opt.Servers = [];
        opt.DefaultHttpClient = new(ScalarTarget.Http, ScalarClient.Http11);
    });

    app.MapOpenApi(); // 映射OpenApi文档路径
}
```

## API路由规则详解

### 标准路由格式

**规则**: `api/{模块名称}/{控制器名称}/{方法名称}`

**示例**:
- 模块: `SSIC.Modules.Sys`
- 控制器: `WeatherForecastController`
- 方法: `GetForecast`
- 生成路由: `api/sys/weatherforecast/getforecast`

### 自定义路由支持

**HTTP属性路由**:
```csharp
[HttpGet("hello")]           // -> api/sys/weatherforecast/hello
[HttpGet("forecast/{days:int}")] // -> api/sys/weatherforecast/forecast/{days:int}
[HttpGet("city/{city}")]     // -> api/sys/weatherforecast/city/{city}
```

**Route属性路由**:
```csharp
[Route("api/custom/path")]   // -> api/custom/path (完全自定义)
```

### 路由优先级

1. **完整自定义路由** (以`api/`开头的Route属性)
2. **HTTP属性模板** (HttpGet/Post等的Template参数)
3. **标准格式路由** (默认的模块/控制器/方法格式)

## 热重载支持

### OpenAPI文档刷新

**触发时机**: 模块热重载时自动刷新

**刷新机制**:
```csharp
private static async Task RefreshOpenApiDocumentation(WebApplication app)
{
    // 1. 刷新ActionDescriptorCollectionProvider
    var actionDescriptorProvider = app.Services.GetService<IActionDescriptorCollectionProvider>();
    if (actionDescriptorProvider is ActionDescriptorCollectionProvider provider)
    {
        var updateMethod = provider.GetType().GetMethod("Update", BindingFlags.NonPublic | BindingFlags.Instance);
        updateMethod?.Invoke(provider, null);
    }

    // 2. 清除OpenAPI相关缓存
    // 3. 通知文档更新
}
```

## 配置文件支持

### modulesettings.json配置

**模块路径配置**:
```json
{
  "ModulePath": {
    "Enabled": true,
    "DevelopmentBasePath": "F:\\SSIC\\Host",
    "ModulePath": "Modules",
    "ScanSubFolders": true,
    "ModuleDirectoryPrefix": "SSIC.Modules.",
    "ScanMode": 3,
    "WildcardPatterns": ["SSIC.Modules.*.dll"]
  }
}
```

## 使用示例

### 模块控制器示例

```csharp
namespace SSIC.Modules.Sys.Controllers
{
    public class WeatherForecastController : ControllerBase
    {
        // 标准路由: api/sys/weatherforecast/get
        [HttpGet]
        public IActionResult Get() { ... }

        // 自定义路由: api/sys/weatherforecast/hello  
        [HttpGet("hello")]
        public IActionResult Hello() { ... }

        // 参数路由: api/sys/weatherforecast/forecast/{days}
        [HttpGet("forecast/{days:int}")]
        public IActionResult GetForecast(int days) { ... }
    }
}
```

### 服务注册示例

```csharp
// 自动注册服务
public class WeatherService : IWeatherService, IScoped
{
    // 自动注册为Scoped生命周期
}

// 模块启动配置
public class SysModule : IStartups
{
    public void ConfigureServices(IServiceCollection services)
    {
        // 模块特定服务注册
    }
}
```

## 总结

该架构通过以下核心组件实现了完整的模块化API文档系统:

1. **ServiceCollectionExtensions**: 提供依赖注入和模块服务注册
2. **ModuleRoutePrefixConvention**: 统一路由规则管理
3. **DynamicModuleDocumentTransformer**: 动态生成OpenAPI文档
4. **BearerSecuritySchemeTransformer**: JWT认证集成
5. **Scalar UI**: 现代化的API文档界面

所有组件协同工作，确保动态加载的模块能够自动生成符合规范的API文档，并支持热重载和实时更新。
