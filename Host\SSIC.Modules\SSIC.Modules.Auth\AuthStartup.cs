using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using SSIC.Infrastructure.Startups;

namespace SSIC.Modules.Auth
{
    /// <summary>
    /// Auth模块启动配置
    /// </summary>
    [Startup(1)]
    public class AuthStartup : IStartups
    {
        /// <summary>
        /// 配置服务
        /// </summary>
        /// <param name="services">服务集合</param>
        public void ConfigureServices(IServiceCollection services)
        {
            // 注册Auth模块特定的服务
            // 例如：认证服务、JWT服务等
            
            // 添加控制器支持
            services.AddControllers();
            
            // 这里可以添加其他Auth模块特定的服务注册
            // services.AddScoped<IAuthService, AuthService>();
            // services.AddScoped<IJwtService, JwtService>();
        }

        /// <summary>
        /// 配置应用程序
        /// </summary>
        /// <param name="app">Web应用程序</param>
        public void Configure(WebApplication app)
        {
            // 配置Auth模块特定的中间件
            // 例如：认证中间件、授权中间件等
            
            // 这里可以添加其他Auth模块特定的配置
        }
    }
}
