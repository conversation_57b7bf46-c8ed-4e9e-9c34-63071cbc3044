{"@t":"2025-07-29T09:07:06.7566669Z","@mt":"热插拔服务未注册，跳过初始化","@l":"Warning","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:07:07.1017687Z","@mt":"在UseEndpoints中发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:07:07.2746530Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:07:07.2851469Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:07:07.8972510Z","@mt":"Now listening on: {address}","address":"https://localhost:57851","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:07:07.9022993Z","@mt":"Now listening on: {address}","address":"http://localhost:57850","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:07:08.0243274Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:07:08.0277124Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:07:08.0309926Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:22:09.3482386Z","@mt":"热插拔服务未注册，跳过初始化","@l":"Warning","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:22:09.4823494Z","@mt":"在UseEndpoints中发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:22:09.6261445Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:22:09.6307101Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:22:10.0387468Z","@mt":"Now listening on: {address}","address":"https://localhost:57851","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:22:10.0441218Z","@mt":"Now listening on: {address}","address":"http://localhost:57850","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:22:10.1147035Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:22:10.1171820Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:22:10.1192279Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:22:18.2683903Z","@mt":"首次请求触发端点刷新","@tr":"b2bacb0fce71761cdfd0f6115f8d834f","@sp":"52f91e6d058d7795","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEEG5KRL4RT:00000001","RequestPath":"/","ConnectionId":"0HNEEG5KRL4RT","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:22:18.2789681Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"b2bacb0fce71761cdfd0f6115f8d834f","@sp":"52f91e6d058d7795","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEEG5KRL4RT:00000001","RequestPath":"/","ConnectionId":"0HNEEG5KRL4RT","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:23:27.9494526Z","@mt":"热插拔服务未注册，跳过初始化","@l":"Warning","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:23:28.0710630Z","@mt":"在UseEndpoints中发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:23:28.1930170Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:23:28.1969353Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:23:28.5711730Z","@mt":"Now listening on: {address}","address":"https://localhost:57851","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:23:28.5810567Z","@mt":"Now listening on: {address}","address":"http://localhost:57850","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:23:28.6613598Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:23:28.6650893Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:23:28.6678999Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:23:53.0966203Z","@mt":"热插拔服务未注册，跳过初始化","@l":"Warning","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:23:53.2209057Z","@mt":"在UseEndpoints中发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:23:53.3314826Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:23:53.3366327Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:23:53.7178790Z","@mt":"Now listening on: {address}","address":"https://localhost:57851","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:23:53.7215698Z","@mt":"Now listening on: {address}","address":"http://localhost:57850","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:23:53.8034337Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:23:53.8060979Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:23:53.8092662Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:24:00.7527061Z","@mt":"首次请求触发端点刷新","@tr":"3c469dcc4081a885504815decf9195a8","@sp":"3f30621d1948dca2","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEEG6JFGPA2:00000001","RequestPath":"/","ConnectionId":"0HNEEG6JFGPA2","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:24:00.7676161Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"3c469dcc4081a885504815decf9195a8","@sp":"3f30621d1948dca2","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEEG6JFGPA2:00000001","RequestPath":"/","ConnectionId":"0HNEEG6JFGPA2","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:24:08.7542511Z","@mt":"开始转换OpenAPI文档以包含动态模块...","@tr":"3478716bd52700921f1edca70c645597","@sp":"f14fe6e489bc3af4","SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEEG6JFGPA2:0000000F","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEEG6JFGPA2","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:24:08.7582958Z","@mt":"发现 {Count} 个活跃模块程序集","@tr":"3478716bd52700921f1edca70c645597","@sp":"f14fe6e489bc3af4","Count":1,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEEG6JFGPA2:0000000F","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEEG6JFGPA2","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:24:08.7702053Z","@mt":"OpenAPI文档转换完成，当前包含 {PathCount} 个路径","@tr":"3478716bd52700921f1edca70c645597","@sp":"f14fe6e489bc3af4","PathCount":12,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEEG6JFGPA2:0000000F","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEEG6JFGPA2","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:29:26.5123727Z","@mt":"热插拔服务未注册，跳过初始化","@l":"Warning","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:29:26.6596615Z","@mt":"在UseEndpoints中发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:29:26.7805408Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:29:26.7845804Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:29:27.2285253Z","@mt":"Now listening on: {address}","address":"https://localhost:57851","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:29:27.2318089Z","@mt":"Now listening on: {address}","address":"http://localhost:57850","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:29:27.2967717Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:29:27.2999146Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:29:27.3024380Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:30:13.8047270Z","@mt":"热插拔服务未注册，跳过初始化","@l":"Warning","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:30:13.9588918Z","@mt":"在UseEndpoints中发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:30:14.0827283Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:30:14.0891856Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:30:14.4676034Z","@mt":"Now listening on: {address}","address":"https://localhost:57851","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:30:14.4718326Z","@mt":"Now listening on: {address}","address":"http://localhost:57850","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:30:14.5489367Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:30:14.5511892Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:30:14.5533502Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:33:05.0542880Z","@mt":"热插拔服务未注册，跳过初始化","@l":"Warning","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:33:05.1224237Z","@mt":"在UseEndpoints中发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:33:05.1922514Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:33:05.1958255Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:33:05.3227227Z","@mt":"Now listening on: {address}","address":"http://localhost:57850","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:33:05.3250184Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:33:05.3261845Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:33:05.3274928Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:33:52.5304933Z","@mt":"热插拔服务未注册，跳过初始化","@l":"Warning","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:33:52.5851321Z","@mt":"在UseEndpoints中发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:33:52.6578264Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:33:52.6617504Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:33:52.7977200Z","@mt":"Now listening on: {address}","address":"http://localhost:57850","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:33:52.7997362Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:33:52.8008046Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:33:52.8020172Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:38:00.2665130Z","@mt":"热插拔服务未注册，跳过初始化","@l":"Warning","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:38:00.4107079Z","@mt":"在UseEndpoints中发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:38:00.5481571Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:38:00.5532483Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:38:00.9037595Z","@mt":"Now listening on: {address}","address":"https://localhost:57851","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:38:00.9145958Z","@mt":"Now listening on: {address}","address":"http://localhost:57850","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:38:00.9722169Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:38:00.9759770Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:38:00.9792349Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:38:02.8089546Z","@mt":"首次请求触发端点刷新","@tr":"b82a6ed96b1c284f8389d89f1f85b582","@sp":"e16b2ed51c65af84","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEEGEE33O99:00000001","RequestPath":"//scalar/v1","ConnectionId":"0HNEEGEE33O99","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:38:02.8266022Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"b82a6ed96b1c284f8389d89f1f85b582","@sp":"e16b2ed51c65af84","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEEGEE33O99:00000001","RequestPath":"//scalar/v1","ConnectionId":"0HNEEGEE33O99","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:38:30.2553370Z","@mt":"热插拔服务未注册，跳过初始化","@l":"Warning","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:38:30.3940268Z","@mt":"在UseEndpoints中发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:38:30.5293492Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:38:30.5336065Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:38:30.9774958Z","@mt":"Now listening on: {address}","address":"https://localhost:57851","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:38:30.9819276Z","@mt":"Now listening on: {address}","address":"http://localhost:57850","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:38:31.0665690Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:38:31.0693702Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:38:31.0718983Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:38:32.7694122Z","@mt":"首次请求触发端点刷新","@tr":"4d80fae6d27ed70f7834991b906fbf94","@sp":"a0549be2a7484774","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEEGEN0PCQK:00000001","RequestPath":"/scalar/v1","ConnectionId":"0HNEEGEN0PCQK","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:38:32.7798007Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"4d80fae6d27ed70f7834991b906fbf94","@sp":"a0549be2a7484774","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEEGEN0PCQK:00000001","RequestPath":"/scalar/v1","ConnectionId":"0HNEEGEN0PCQK","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:38:33.2364870Z","@mt":"开始转换OpenAPI文档以包含动态模块...","@tr":"83624d38ace474edc3657fdeff832331","@sp":"d4a9c1c4d8ad0095","SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEEGEN0PCQK:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEEGEN0PCQK","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:38:33.2412457Z","@mt":"发现 {Count} 个活跃模块程序集","@tr":"83624d38ace474edc3657fdeff832331","@sp":"d4a9c1c4d8ad0095","Count":1,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEEGEN0PCQK:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEEGEN0PCQK","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:38:33.2553402Z","@mt":"OpenAPI文档转换完成，当前包含 {PathCount} 个路径","@tr":"83624d38ace474edc3657fdeff832331","@sp":"d4a9c1c4d8ad0095","PathCount":12,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEEGEN0PCQK:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEEGEN0PCQK","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
