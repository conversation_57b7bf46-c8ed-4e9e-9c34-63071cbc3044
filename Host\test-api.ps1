# 测试API连接脚本

Write-Host "开始测试API连接..." -ForegroundColor Green

# 测试端口5246（HostServer默认端口）
$urls = @(
    "http://localhost:5246/api/auth/role/test",
    "http://localhost:5246/api/auth/role/list", 
    "http://localhost:5246/api/auth/auth/login",
    "http://localhost:5246/scalar/v1"
)

foreach ($url in $urls) {
    Write-Host "测试URL: $url" -ForegroundColor Yellow
    try {
        $response = Invoke-WebRequest -Uri $url -Method GET -TimeoutSec 5
        Write-Host "✓ 成功 - 状态码: $($response.StatusCode)" -ForegroundColor Green
        Write-Host "响应内容: $($response.Content.Substring(0, [Math]::Min(200, $response.Content.Length)))" -ForegroundColor Cyan
    }
    catch {
        Write-Host "✗ 失败 - 错误: $($_.Exception.Message)" -ForegroundColor Red
    }
    Write-Host "---" -ForegroundColor Gray
}

Write-Host "测试完成" -ForegroundColor Green
